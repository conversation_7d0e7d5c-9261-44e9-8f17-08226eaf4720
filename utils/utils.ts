/**
 * Converts a string value to a numeric value
 * If value is a digit, returns the digit (or 10 for 0)
 * If value is a letter, returns its position in alphabet mod 10 (or 10 for J, T, etc.)
 * Otherwise returns 0
 */
export const getNumericValue = (value: string): number => {
  if (/^\d+$/.test(value)) {
    return parseInt(value, 10) || 10;
  } else if (/^[a-zA-Z]$/.test(value)) {
    const charCode = value.toUpperCase().charCodeAt(0) - 64;
    return (charCode % 10) || 10;
  } else {
    return 0;
  }
};

/**
 * Converts a number (0-9) to the corresponding Heavenly Stem (天干)
 * @param num - A number from 0 to 9
 * @returns The corresponding Heavenly Stem character
 */
export const getTianGan = (num: number): string => {
  const stems = ['癸', '甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬'];
  return stems[num % 10];
};

/**
 * Determines if a number is Yin or Yang
 * @param num - The input number
 * @returns "阴" for even numbers (Yin) or "阳" for odd numbers (Yang)
 */
export const getYinYang = (num: number): string => {
  return num % 2 === 0 ? "阴" : "阳";
};

/**
 * Determines the Wu Xing (Five Elements) based on a given number
 * @param num - The input number
 * @returns The corresponding Wu Xing element in Chinese
 */
export const getWuXing = (num: number): string => {
  const elements = ['水', '木', '木', '火', '火', '土', '土', '金', '金', '水'];
  return elements[num % 10];
};

export const getWuXingRelationship = (element1: string, element2: string): string => {
  const relationships = {
    '木': { '火': '生', '土': '克', '金': '被克', '水': '泄' },
    '火': { '土': '生', '金': '克', '水': '被克', '木': '泄' },
    '土': { '金': '生', '水': '克', '木': '被克', '火': '泄' },
    '金': { '水': '生', '木': '克', '火': '被克', '土': '泄' },
    '水': { '木': '生', '火': '克', '土': '被克', '金': '泄' }
  };

  if (element1 === element2) {
    return '同';
  }

  return relationships[element1][element2];
};

export const getWuXingColor = (wuXing: string): string => {
  const colorMap: { [key: string]: string } = {
    '木': '#4CAF50',  // Green
    '火': '#FF5722',  // Red
    '土': '#B97A57',  // Yellow
    '金': '#dbcb1a',  // Gold
    '水': '#2196F3'   // Blue
  };

  return colorMap[wuXing] || '#000000';  // Default to black if not found
};

// 将小时和分钟转换为12时辰
// 子时(23:00-00:59)为1，丑时(01:00-02:59)为2，以此类推
export const convertHourToEarthlyBranch = (hourNum: number): number => {
        // 默认设置为子时，以防止未定义的情况
        let earthlyBranchHour = 1;
      
        if (hourNum >= 23 || hourNum < 1) {
          earthlyBranchHour = 1; // 子时 (23:00-00:59)
        } else if (hourNum >= 1 && hourNum < 3) {
          earthlyBranchHour = 2; // 丑时 (01:00-02:59)
        } else if (hourNum >= 3 && hourNum < 5) {
          earthlyBranchHour = 3; // 寅时 (03:00-04:59)
        } else if (hourNum >= 5 && hourNum < 7) {
          earthlyBranchHour = 4; // 卯时 (05:00-06:59)
        } else if (hourNum >= 7 && hourNum < 9) {
          earthlyBranchHour = 5; // 辰时 (07:00-08:59)
        } else if (hourNum >= 9 && hourNum < 11) {
          earthlyBranchHour = 6; // 巳时 (09:00-10:59)
        } else if (hourNum >= 11 && hourNum < 13) {
          earthlyBranchHour = 7; // 午时 (11:00-12:59)
        } else if (hourNum >= 13 && hourNum < 15) {
          earthlyBranchHour = 8; // 未时 (13:00-14:59)
        } else if (hourNum >= 15 && hourNum < 17) {
          earthlyBranchHour = 9; // 申时 (15:00-16:59)
        } else if (hourNum >= 17 && hourNum < 19) {
          earthlyBranchHour = 10; // 酉时 (17:00-18:59)
        } else if (hourNum >= 19 && hourNum < 21) {
          earthlyBranchHour = 11; // 戌时 (19:00-20:59)
        } else if (hourNum >= 21 && hourNum < 23) {
          earthlyBranchHour = 12; // 亥时 (21:00-22:59)
        }
        return earthlyBranchHour;
};

//将农历月份表达方式转换为数字
export const convertLunarMonthToNumber = (month: string): number => {
  const lunarMonths = ['正', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二'];
  return lunarMonths.indexOf(month) + 1;
};

//将数字转换为农历月份表达方式
export const convertNumberToLunarMonth = (month: number): string => {
  const lunarMonths = ['正', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二'];
  return lunarMonths[month - 1];
};

//将数字每位相加，最终得到一个数字，如果这个数字大于10，再将这个数字每位相加，直到得到一个数字
export const sumNumber = (num: number): number => {
  let sum = num;
  while (sum >= 10) {
    sum = sum.toString().split('').reduce((sum, digit) => sum + parseInt(digit), 0);
  }
  return sum;
};


//将时辰数转换为时辰名称，0为子时，1为丑时，以此类推
export const convertHourToHourName = (hourNum: number): string => {
  const hourNames = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
  return hourNames[hourNum - 1];
};

//将农历日期数字转换为农历日期，输入日期的数字序号，返回农历日期的名称
export const convertLunarDateToLunarString = (day: number): string => {
  const lunarDays = ['初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十', '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十', '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'];
  return lunarDays[day - 1];
};


