import * as React from 'react';
import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Lunar, sun_moon, SSQ } from './FullLunar';
import { convertNumberToLunarMonth } from '../utils/utils'; // 假设有一个工具函数来转换数字到农历月份

/**
 * LunarCalendar component - Displays lunar calendar information
 */
export const LunarCalendar = ({ year, month }: { year: number; month: number }) => {
  const [lunarData, setLunarData] = useState<any>(null);
  // 添加选中日期状态
  const [selectedDay, setSelectedDay] = useState<number | null>(null);
  // 添加时辰偏移状态
  const [timePeriodOffset, setTimePeriodOffset] = useState<number>(0);

  useEffect(() => {
    // Create a new Lunar instance
    const lunar = new Lunar();

    // Calculate lunar calendar data for the specified year and month
    lunar.yueLiCalc(year, month);
    // Log lunar data to Reactotron for debugging
    if (__DEV__) {
      const Reactotron = require('reactotron-react-native').default;
      Reactotron.log('Lunar data:', lunar);
    }
    setLunarData(lunar);
    // 重置选中日期
    setSelectedDay(null);
  }, [year, month]);

  if (!lunarData) {
    return (
      <View style={styles.loading}>
        <Text>Loading lunar data...</Text>
      </View>
    );
  }

  // 处理日期选择
  const handleDaySelect = (index: number) => {
    if (selectedDay === index) {
      // 如果已经选中，则取消选中
      setSelectedDay(null);
    } else {
      // 否则选中该日期
      setSelectedDay(index);
    }
  };

  // 获取当前选中日期的信息
  const getSelectedDayInfo = () => {
    if (selectedDay === null) return null;
    return lunarData.lun[selectedDay];
  };



  // Format day information for display
  const renderDays = () => {
    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < lunarData.w0; i++) {
      days.push(
        <View key={`empty-${i}`} style={styles.emptyDay} />
      );
    }

    // Add cells for each day of the month
    for (let i = 0; i < lunarData.dn; i++) {
      const dayInfo = lunarData.lun[i];
      const isSelected = selectedDay === i;

      days.push(
        <TouchableOpacity
          key={`day-${i}`}
          style={[styles.day, isSelected && styles.selectedDay]}
          onPress={() => handleDaySelect(i)}
        >
            <Text style={[styles.dayNumber, isSelected && styles.selectedDayText]} numberOfLines={1} ellipsizeMode="tail">
              {dayInfo.d}
            </Text>
            {dayInfo.Ljq ? (
              <Text style={[styles.solarTerm, isSelected && styles.selectedDayText]} numberOfLines={1} ellipsizeMode="tail">
                {dayInfo.Ljq}
              </Text>
            ) : dayInfo.Ldc === '初一' ? (
              <Text style={[styles.lunarMonth, isSelected && styles.selectedDayText]} numberOfLines={1} ellipsizeMode="tail">
                {dayInfo.Lleap}{dayInfo.Lmc}月
              </Text>
            ) : (
              <Text style={[styles.lunarDay, isSelected && styles.selectedDayText]} numberOfLines={1} ellipsizeMode="tail">
                {dayInfo.Ldc}
              </Text>
            )}
            <Text style={[styles.lunarDay, isSelected && styles.selectedDayText]} numberOfLines={1} ellipsizeMode="tail">
              {dayInfo.Lday2}
            </Text>
        </TouchableOpacity>
      );
    }

    return days;
  };

  // 添加一个函数来获取本月的节气信息
  const getMonthSolarTerms = () => {
    const solarTerms = [];

    for (let i = 0; i < lunarData.dn; i++) {
      const dayInfo = lunarData.lun[i];
      if (dayInfo.jqmc) {
        solarTerms.push({
          name: dayInfo.jqmc,
          day: dayInfo.d,
          time: dayInfo.jqsj
        });
      }
    }

    return solarTerms;
  };

  // 获取当前时间的干支


  // 添加调整时辰的函数
  const adjustTimePeriod = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setTimePeriodOffset(prev => prev - 1);
    } else {
      setTimePeriodOffset(prev => prev + 1);
    }
  };

  
  


  const selectedDayInfo = getSelectedDayInfo();
  const monthGanZhi = getMonthGanZhi(getSelectedDayInfo, new Date().getHours() + (timePeriodOffset * 2));
  
  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View id="calendarSection" style={styles.calendarSection}>
        <View style={styles.header}>
          <Text style={styles.title}>
            {year > 0 ? '公元 ' + year : '公元前 ' + ((year * -1) + 1)} 年 {month} 月 - {lunarData.Ly}年 {lunarData.ShX} {selectedDay !== null && monthGanZhi ? `(${monthGanZhi}月)` : ''}
          </Text>
        </View>

        <View style={styles.weekdays}>
          <Text style={styles.weekday}>日</Text>
          <Text style={styles.weekday}>一</Text>
          <Text style={styles.weekday}>二</Text>
          <Text style={styles.weekday}>三</Text>
          <Text style={styles.weekday}>四</Text>
          <Text style={styles.weekday}>五</Text>
          <Text style={styles.weekday}>六</Text>
        </View>
        <View style={styles.calendar}>
          {renderDays()}
        </View>
      </View>

      {/* 节气信息展示区域 - 始终显示 */}
      <View style={styles.solarTermsContainer}>
        {getMonthSolarTerms().length > 0 ? (
          getMonthSolarTerms().map((term, index) => (
            <View key={index} style={styles.solarTermItem}>
              <View style={styles.solarTermNameWrapper}>
                <Text style={styles.solarTermName}>{term.name}</Text>
              </View>
              <View style={styles.solarTermDateWrapper}>
                <Text style={styles.solarTermDate}>{term.day} 日</Text>
                <Text style={styles.solarTermTime}>{term.time}</Text>
              </View>
            </View>
          ))
        ) : (
          <Text style={styles.noSolarTerm}>本月无节气</Text>
        )}
      </View>
      
      {/* 不再需要额外的空白边距 */}

      {/* 选中日期详细信息 - 放在日历下方 */}
      {selectedDayInfo && (
        <View style={styles.infoSection}>
          <View style={styles.selectedDayInfoContainer}>
            <Text style={styles.selectedDayInfoTitle}>
              {year} 年 {month} 月 {selectedDayInfo.d} 日 {selectedDayInfo.Lyear2}年 {selectedDayInfo.Lleap}{selectedDayInfo.Lmc}月 {selectedDayInfo.Ldc}</Text>

            {/* 四柱信息与时辰控制放在同一行 */}
            <View style={styles.fourPillarsRow}>
              <TouchableOpacity
                style={styles.timeButton}
                onPress={() => adjustTimePeriod('prev')}
              >
                <Text style={styles.timeButtonText}>◀</Text>
              </TouchableOpacity>

              <Text style={styles.selectedDayInfoContent}>
                四柱: {selectedDayInfo.Lyear2} {getMonthGanZhi(getSelectedDayInfo(), new Date().getHours() + (timePeriodOffset * 2))} {selectedDayInfo.Lday2} {getTimeGanZhi(getSelectedDayInfo(), ((Math.floor((new Date().getHours() + 1)/ 2) + timePeriodOffset + 12) % 12) + 1)}
              </Text>

              <TouchableOpacity
                style={styles.timeButton}
                onPress={() => adjustTimePeriod('next')}
              >
                <Text style={styles.timeButtonText}>▶</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
    </ScrollView>
  );
};
/**
 * SunMoonInfo component - Displays sun and moon position information
 */
export const SunMoonInfo = ({
  date,
  longitude,
  latitude,
  elevation = 0
}: {
  date: Date;
  longitude: number;
  latitude: number;
  elevation?: number;
}) => {
  const [sunMoonData, setSunMoonData] = useState<any>(null);

  useEffect(() => {
    // Convert input date to Julian Day
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();
    const second = date.getSeconds();

    // Create JD object (from FullLunar.js)
    const JD: any = { Y: year, M: month, D: day, h: hour, m: minute, s: second };

    // Get Julian day number (approximate implementation)
    const julianDay = Math.floor(365.25 * (year + 4716)) + Math.floor(30.6001 * (month + 1)) + day +
      (hour + minute / 60 + second / 3600) / 24 - 1524.5;

    // Calculate sun_moon data
    const sm = new sun_moon();

    // Convert longitude and latitude to radians
    const lonRad = longitude * Math.PI / 180;
    const latRad = latitude * Math.PI / 180;

    // Calculate (T is Julian centuries since J2000.0)
    sm.calc(julianDay, lonRad, latRad, elevation / 1000); // elevation in km

    setSunMoonData(sm);
  }, [date, longitude, latitude, elevation]);

  if (!sunMoonData) {
    return (
      <View style={styles.loading}>
        <Text>Calculating sun and moon positions...</Text>
      </View>
    );
  }

  // Helper function to convert radians to degrees
  const radToDeg = (rad: number) => rad * 180 / Math.PI;

  return (
    <ScrollView style={styles.container}>
      <View style={styles.infoSection}>
        <Text style={styles.sectionTitle}>Sun Position</Text>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Azimuth:</Text>
          <Text style={styles.infoValue}>{radToDeg(sunMoonData.sPJ).toFixed(2)}°</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Altitude:</Text>
          <Text style={styles.infoValue}>{radToDeg(sunMoonData.sPW).toFixed(2)}°</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Hour Angle:</Text>
          <Text style={styles.infoValue}>{radToDeg(sunMoonData.sShiJ).toFixed(2)}°</Text>
        </View>
      </View>

      <View style={styles.infoSection}>
        <Text style={styles.sectionTitle}>Moon Position</Text>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Azimuth:</Text>
          <Text style={styles.infoValue}>{radToDeg(sunMoonData.mPJ).toFixed(2)}°</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Altitude:</Text>
          <Text style={styles.infoValue}>{radToDeg(sunMoonData.mPW).toFixed(2)}°</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Hour Angle:</Text>
          <Text style={styles.infoValue}>{radToDeg(sunMoonData.mShiJ).toFixed(2)}°</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Phase:</Text>
          <Text style={styles.infoValue}>{(sunMoonData.mIll * 100).toFixed(1)}%</Text>
        </View>
      </View>

      <View style={styles.infoSection}>
        <Text style={styles.sectionTitle}>Time Information</Text>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Mean Solar Time:</Text>
          <Text style={styles.infoValue}>{formatTime(sunMoonData.pty)}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>True Solar Time:</Text>
          <Text style={styles.infoValue}>{formatTime(sunMoonData.zty)}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Equation of Time:</Text>
          <Text style={styles.infoValue}>{(sunMoonData.sc * 86400).toFixed(1)} sec</Text>
        </View>
      </View>
    </ScrollView>
  );
};

// Helper function to format time
const formatTime = (julianDay: number) => {
  const totalHours = (julianDay % 1) * 24;
  const hours = Math.floor(totalHours);
  const minutes = Math.floor((totalHours - hours) * 60);
  const seconds = Math.floor(((totalHours - hours) * 60 - minutes) * 60);

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

const styles = StyleSheet.create({

  loading: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 15,
    backgroundColor: '#f0f0f0',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  weekdays: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    paddingVertical: 10,
  },
  weekday: {
    flex: 1,
    textAlign: 'center',
    fontWeight: 'bold',
  },

  emptyDay: {
    width: '14.28%',
    aspectRatio: 1,
  },
  dayNumber: {
    fontSize: 14, // 减小字体大小
    fontWeight: 'bold',
    textAlign: 'center', // 确保文本居中
    width: '100%', // 确保文本有完整宽度
  },
  lunarDay: {
    fontSize: 11, // 减小字体大小
    color: '#666',
    textAlign: 'center', // 确保文本居中
    width: '100%', // 确保文本有完整宽度
  },
  lunarMonth: {
    fontSize: 11, // 减小字体大小
    color: '#0066cc',
    fontWeight: 'bold',
    textAlign: 'center', // 确保文本居中
    width: '100%', // 确保文本有完整宽度
  },
  solarTerm: {
    fontSize: 11, // 减小字体大小
    color: '#ff6b00',
    textAlign: 'center', // 确保文本居中
    width: '100%', // 确保文本有完整宽度
  },
  infoSection: {
    marginTop: 0,
    marginBottom: 0,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginVertical: 10,
  },
  infoRow: {
    flexDirection: 'row',
    paddingVertical: 5,
    borderBottomWidth: 0.5,
    borderBottomColor: '#e0e0e0',
  },
  infoLabel: {
    flex: 1,
    fontSize: 14,
  },
  infoValue: {
    flex: 1,
    fontSize: 14,
    textAlign: 'right',
  },
  // 节气展示相关样式
  solarTermsContainer: {
    marginTop: 0,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingTop: 0,
    paddingBottom: 0,
    paddingHorizontal: 15,
    marginHorizontal: 10,
  },
  solarTermsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  solarTermItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 5,
    borderBottomWidth: 0.5,
    borderBottomColor: '#e0e0e0',
  },
  solarTermNameWrapper: {
    flex: 1,
    marginRight: 10,
  },
  solarTermName: {
    fontSize: 14,
    color: '#ff6b00',
    fontWeight: 'bold',
  },
  solarTermDateWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    flexShrink: 0,
    width: '40%',
  },
  solarTermDate: {
    fontSize: 14,
    color: '#666',
    marginRight: 20,
  },
  solarTermTime: {
    fontSize: 14,
    color: '#666',
  },
  noSolarTerm: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
    textAlign: 'center',
    paddingVertical: 10,
  },
  day: {
    width: '14.28%', // 7 days per week
    minHeight: 8,   // 基础高度，内容多时可自动增高
    padding: 3, // 减小内边距以获得更多空间
    borderWidth: 0.5,
    borderColor: '#f0f0f0',
    justifyContent: 'center', // 垂直居中
    alignItems: 'center',
    flexDirection: 'column', // 垂直排列内容
  },
  selectedDay: {
    backgroundColor: '#e6f7ff',
    outlineWidth: 1,           // 选中时外扩描边
    outlineColor: '#1890ff',
  },
  selectedDayText: {
    color: '#1890ff',
  },
  selectedDayInfoContainer: {
    padding: 15,
    backgroundColor: '#f9f9f9',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 0,
    marginHorizontal: 0,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 0,
  },
  selectedDayInfoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 0,
    textAlign: 'center',
  },
  selectedDayInfoContent: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    flex: 1,
  },
  // 添加时辰控制相关样式
  timeControl: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
    paddingHorizontal: 10,
  },
  timeButton: {
    backgroundColor: '#64B5F6',
    borderRadius: 4,
    padding: 8,
    marginHorizontal: 5,
  },
  timeButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  fourPillarsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
    paddingHorizontal: 5,
  },
  calendarBottomMargin: {
    height: 0,
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  contentContainer: {
    flexGrow: 1,
    paddingBottom: 0,
    flex: 0, // 确保容器不会拉伸
    height: 'auto', // 根据内容自动调整高度
  },
  calendarSection: {
    marginBottom: 0,
    marginTop: 0,
    paddingBottom: 0,
    flex: 0,
    height: 'auto', // 根据内容自动调整高度
  },
  calendarContainer: {
    width: '100%',           // 让容器宽度占满父级，避免内容被裁剪
    alignItems: 'center',   // 水平居中 calendar
    marginBottom: 0,       // 控制日历与下方内容的间距
  },
  calendar: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 10,
    paddingTop: 0,
    alignItems: 'flex-end', // 确保容器不会被拉伸
    overflow: 'visible', // 允许内容超出容器边界
  },

});


// Export both components as a named export
export { Lunar, sun_moon };  

// 农历转公历的方法，支持闰月输入
export const convertLunarToSolar = (lunarYear: number, lunarMonth: number, lunarDay: number, isLeapMonth: boolean = false) => {
  try {
    // 创建一个新的Lunar实例来计算整年数据
    const lunar = new Lunar();
    
    // 计算目标年份的农历数据
    // 需要遍历一年的所有月份来找到匹配的农历日期
    
    for (let _month = lunarMonth, _year = lunarYear, idx = 0; idx <= 5; _month++, idx) {
      if (_month > 12) {
        _month = 1;
        _year++;
      }
      lunar.yueLiCalc(_year, _month);
      
      // 遍历当月的所有日期
      for (let i = 0; i < lunar.dn; i++) {
        let dayInfo = lunar.lun[i];
        
        // 检查是否匹配目标农历日期

        if (dayInfo.Lmc === convertNumberToLunarMonth(lunarMonth) && 
            dayInfo.Ldi === lunarDay - 1 && // Ldi是从0开始的
            (isLeapMonth ? dayInfo.Lleap === '闰' : dayInfo.Lleap === '')) {
          
          return {
            ...dayInfo
          };
        }
      }
    }
    
    return {};
  } catch (error) {
    console.error('农历转公历转换失败:', error);
    return {};
  }
};

// 获取指定日期前后的节气
const getSolarTermsAroundDate = (julianDay: number) => {
  try {
    // 使用SSQ（实朔实气计算器）来获取节气信息
    // 这里需要访问全局的SSQ对象
    if (typeof SSQ !== 'undefined' && SSQ.ZQ) {
      const solarTerms = [];
      
      // 节气名称数组
      const termNames = [
        '冬至', '小寒', '大寒', '立春', '雨水', '惊蛰',
        '春分', '清明', '谷雨', '立夏', '小满', '芒种',
        '夏至', '小暑', '大暑', '立秋', '处暑', '白露',
        '秋分', '寒露', '霜降', '立冬', '小雪', '大雪'
      ];
      
      // 找到当前日期在节气表中的位置
      let currentIndex = -1;
      for (let i = 0; i < SSQ.ZQ.length; i++) {
        if (SSQ.ZQ[i] > julianDay) {
          currentIndex = i;
          break;
        }
      }
      
      if (currentIndex > 0) {
        // 前一个节气
        const prevIndex = (currentIndex - 1) % 24;
        const prevTerm = {
          name: termNames[prevIndex],
          date: SSQ.ZQ[currentIndex - 1],
          julianDay: SSQ.ZQ[currentIndex - 1]
        };
        
        // 下一个节气
        const nextIndex = currentIndex % 24;
        const nextTerm = {
          name: termNames[nextIndex],
          date: SSQ.ZQ[currentIndex],
          julianDay: SSQ.ZQ[currentIndex]
        };
        
        return {
          previous: prevTerm,
          next: nextTerm
        };
      }
    }
    
    return {
      previous: null,
      next: null
    };
  } catch (error) {
    console.error('获取节气信息失败:', error);
    return {
      previous: null,
      next: null
    };
  }
};

  // 获取月干支，考虑节气交接时刻
  export const getMonthGanZhi = (selectedDayInfo:any, currentHour: number) => {
    if (!selectedDayInfo) return '';

    // 如果当天有节气
    if (selectedDayInfo.jqmc && selectedDayInfo.jqsj) {
      // 获取当前选择的时辰
      const adjustedHour = ((currentHour % 24) + 24) % 24; // 确保在0-23范围内

      // 解析节气时间（格式通常为"23:45:12"）
      const jqTime = selectedDayInfo.jqsj.split(':');
      const jqHour = parseInt(jqTime[0], 10);

      // 如果当前时辰在节气时间之前，使用前一个月干支
      if (adjustedHour < jqHour) {
        // 获取前一个月干支 - 这里需要知道前一个月干支是什么
        // 简单的方法是查看当月第一天的前一天，或者尝试从其他日期获取
        // 但这需要额外的计算，这里我们简化处理

        // 通过干支推算前一个月干支
        const ganZhi = selectedDayInfo.Lmonth2;

        // 干支序列
        const gan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
        const zhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

        // 提取当前月干支的天干和地支
        const monthGan = ganZhi.charAt(0);
        const monthZhi = ganZhi.charAt(1);

        // 计算前一个月的干支
        const ganIndex = gan.indexOf(monthGan);
        const zhiIndex = zhi.indexOf(monthZhi);

        // 计算前一个干支索引（注意循环）
        const prevGanIndex = (ganIndex - 1 + 10) % 10;
        const prevZhiIndex = (zhiIndex - 1 + 12) % 12;

        return `${gan[prevGanIndex]}${zhi[prevZhiIndex]}`;
      }
    }

    // 默认返回当前选中日期的月干支
    return selectedDayInfo.Lmonth2;
  };

  //timePeriod: 1-12，表示子时到亥时
  // dayInfo: 选中日期的农历信息
  // 返回格式: "甲子时" 或 "乙丑时" 等
  // 注意：时辰从1开始，1表示子时（23:00-00:59），2表示丑时（01:00-02:59），依此
  export const getTimeGanZhi = (dayInfo: any = null, timePeriod: number) => {

    // 十二时辰名称，从子时（23:00-00:59）开始
    const timePeriods = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

    // 选中日期的干支
    const selectedDayInfo = dayInfo ;
    if (selectedDayInfo) {
      // 获取日干（selectedDayInfo.Lday2 的第一个字符）
      const dayGan = selectedDayInfo.Lday2.charAt(0);

      // 根据日干推算时干
      // 甲己日起甲子时，乙庚日起丙子时，丙辛日起戊子时，丁壬日起庚子时，戊癸日起壬子时
      const ganStartMap: { [key: string]: number } = {
        '甲': 0, '己': 0, // 甲己日起甲子时
        '乙': 2, '庚': 2, // 乙庚日起丙子时
        '丙': 4, '辛': 4, // 丙辛日起戊子时
        '丁': 6, '壬': 6, // 丁壬日起庚子时
        '戊': 8, '癸': 8  // 戊癸日起壬子时
      };

      const tiganStart = ganStartMap[dayGan] || 0;
      const tiganIndex = (tiganStart + timePeriod - 1) % 10;
      const tigan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'][tiganIndex];

      return `${tigan}${timePeriods[timePeriod - 1]}`;
    }

    return '';
  };