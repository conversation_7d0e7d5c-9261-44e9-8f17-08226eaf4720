import React, { useState } from 'react';
import { StyleSheet, View, TextInput, TouchableOpacity, Dimensions, Image, Modal, Switch, Clipboard } from 'react-native';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';
import { useColorScheme } from '@/hooks/useColorScheme';
import { getNumericValue, getTianGan, getYinYang, getWuXing, getWuXingRelationship, getWuXingColor, convertHourToEarthlyBranch, convertLunarMonthToNumber, sumNumber, convertHourToHourName, convertLunarDateToLunarString, convertNumberToLunarMonth } from '../utils/utils';
import { Lunar } from './FullLunar';
import { convertLunarToSolar, getMonthGanZhi, getTimeGanZhi} from './LunarComponent'
// Number class to represent each number in the chart
class ChartNumber {
  value: string;
  numValue: number;
  color: string;
  tianGan?: string;
  wuXing?: string;
  yinYang?: string;
  shiShen?: string;
  
  constructor(value: string) {
    this.value = value;
    this.numValue = getNumericValue(value);
    this.tianGan = getTianGan(this.numValue);
    this.wuXing = getWuXing(this.numValue);
    this.yinYang = getYinYang(this.numValue);
    this.color = getWuXingColor(this.wuXing);
  }

  getShiShenRelationship = (target: ChartNumber): string => {
    if (this === target) {
      return "日主";
    }

    const baseYinYang = this.yinYang;
    const targetYinYang = target.yinYang;
    const baseWuXing = this.wuXing;
    const targetWuXing = target.wuXing;

    if (baseWuXing === targetWuXing) {
      return baseYinYang === targetYinYang ? "比肩" : "劫财";
    }
    
    if (!baseWuXing || !targetWuXing) {
      return "未知";
    }

    const relationship = getWuXingRelationship(baseWuXing, targetWuXing);
    switch (relationship) {
      case '泄': // 我生者 (I produce target)
        return baseYinYang === targetYinYang ? "食神" : "伤官";
      case '生': // 生我者 (Target produces me)
        return baseYinYang === targetYinYang ? "偏印" : "正印";
      case '克': // 我克者 (I overcome target)
        return baseYinYang === targetYinYang ? "偏财" : "正财";
      case '被克': // 克我者 (Target overcomes me)
        return baseYinYang === targetYinYang ? "七杀" : "正官";
      default:
        return "未知";
    }
  };
  // Method to display the number with styling
  display() {
    return {
      value: this.value,
      color: this.color,
      tianGan: this.tianGan
    };
  }
}

export const NumberChart = () => {
  const colorScheme = useColorScheme();
  const [inputValue, setInputValue] = useState('');
  const [showChart, setShowChart] = useState(false);
  const [showBaseInfo, setShowBaseInfo] = useState(false);
  const [centerNumbers, setCenterNumbers] = useState<ChartNumber[]>([]);
  const [rightNumbers, setRightNumbers] = useState<ChartNumber[]>([]);
  const [leftNumbers, setLeftNumbers] = useState<ChartNumber[]>([]);
  const [topNumber, setTopNumber] = useState<ChartNumber | null>(null);
  const [bottomNumber, setBottomNumber] = useState<ChartNumber | null>(null);
  const [rightSumNumber, setRightSumNumber] = useState<ChartNumber | null>(null);
  const [leftSumNumber, setLeftSumNumber] = useState<ChartNumber | null>(null);
  const [dateShowString, setDateShowString] = useState('');
  const [siZhuShowString, setSiZhuShowString] = useState('');
  const [gongLiString, setGongLiString] = useState('');

  // Date modal states
  const [showDateModal, setShowDateModal] = useState(false);
  const [year, setYear] = useState('');
  const [month, setMonth] = useState('');
  const [day, setDay] = useState('');
  const [hour, setHour] = useState('');
  const [minute, setMinute] = useState('');
  const [calendarType, setCalendarType] = useState<'Gregorian' | 'Lunar'>('Gregorian');
  const [isLeapMonth, setIsLeapMonth] = useState(false);
  const [name, setName] = useState('');

  const handleDateConfirm = () => {
    if (!year || !month || !day || !hour ) {
      alert('年、月、日、时不能为空');
      return;
    }
    setShowDateModal(false);
    let calculateYear, calculateMonth, calculateDay, calculateHour;
    let _dateShowString = "";
    let _siZhuShowString = "";
    let _gongLiString = '';
    let _dayinfo;
    // 如果是农历，设置计算用的年月日时
    if (calendarType === 'Lunar') {
      calculateYear =Number(year);
      calculateMonth =Number(month);
      calculateDay =Number(day);
      calculateHour = convertHourToEarthlyBranch(Number(hour));
      _dayinfo = convertLunarToSolar(calculateYear, calculateMonth, calculateDay, isLeapMonth);
      if (_dayinfo === null) {
        alert('农历日期转换失败，请检查输入');
        return;
      }
    } else {
          // Create a new Lunar instance
          const lunar = new Lunar();
          // Calculate lunar calendar data for the specified year and month
          lunar.yueLiCalc(Number(year), Number(month));
          if (Number(day) > lunar.dn) {
            alert('日期超出该月范围');
            return;
          };
          _dayinfo = lunar.lun[Number(day) - 1];
          calculateYear = 1984 + _dayinfo.Lyear0;
          calculateMonth = convertLunarMonthToNumber(_dayinfo.Lmc);
          calculateDay = _dayinfo.Ldi + 1;
          calculateHour = convertHourToEarthlyBranch(Number(hour));
    };
    _dateShowString = `农历：${calculateYear}年 ${_dayinfo.Lleap}${_dayinfo.Lmc}月 ${_dayinfo.Ldc} ${convertHourToHourName(calculateHour)}时`;
    _siZhuShowString = `${_dayinfo.Lyear2} ${getMonthGanZhi(_dayinfo, calculateHour)} ${_dayinfo.Lday2} ${getTimeGanZhi(_dayinfo, calculateHour)}`;
    _gongLiString = `公历：${_dayinfo.y}年 ${_dayinfo.m}月 ${_dayinfo.d}日 ${Number(hour)}时${minute ? ` ${Number(minute)}分` : ''}`;

    
    // 计算年、月、日、时的和
    const yearSum = sumNumber(calculateYear).toString();
    const monthSum = sumNumber(calculateMonth).toString();
    const daySum = sumNumber(calculateDay).toString();
    const hourSum = sumNumber(calculateHour).toString();
    const combinedInput = yearSum + monthSum + daySum + hourSum;
    setInputValue(combinedInput);

    setShowChart(true);
    setShowBaseInfo(true);
    calculateChart(combinedInput);
    setDateShowString(_dateShowString);
    setSiZhuShowString(_siZhuShowString);
    setGongLiString(_gongLiString);
  };

  const calculateChart = (valueToUse?: string) => {
    // 确保使用有效的字符串
    const _value = valueToUse || inputValue || '';
    if (_value.length < 4) {
      alert('至少输入4位数字');
      return;
    }
    
    // 当直接点击计算按钮时，清除日期显示字符串
    if (!valueToUse) {
      setDateShowString('');
    }

    const createChartNumber = (value: number) => new ChartNumber(value.toString());

    // Extract the last 4 digits
    const lastFourDigits = _value.slice(-4).split('');
    // Create center numbers
    const center = lastFourDigits.map(num => new ChartNumber(num));
    
    const n_c0_c1 = createChartNumber(center[0].numValue + center[1].numValue);
    const n_c1_c2 = createChartNumber(center[1].numValue + center[2].numValue); // This is the Day Master (日主)
    const n_c2_c3 = createChartNumber(center[2].numValue + center[3].numValue);
    const n_c0_c2 = createChartNumber(center[0].numValue + center[2].numValue);
    const n_c1_c3 = createChartNumber(center[1].numValue + center[3].numValue);
    const n_c0_c3 = createChartNumber(center[0].numValue + center[3].numValue);
    const n_total_sum = createChartNumber(center[0].numValue + center[1].numValue + center[2].numValue + center[3].numValue);

    const n_top = createChartNumber(n_c0_c1.numValue + n_c0_c2.numValue);
    const n_right_sum = createChartNumber(n_c0_c1.numValue + n_c1_c2.numValue + n_c2_c3.numValue);
    const n_left_sum = createChartNumber(n_c0_c2.numValue + n_c1_c3.numValue + n_c0_c3.numValue);

    const allNumbers = [
      n_c0_c1, n_c1_c2, n_c2_c3,
      n_c0_c2, n_c1_c3, n_c0_c3,
      n_total_sum, n_top, n_right_sum, n_left_sum
    ];

    const dayMaster = n_c1_c2;
  
    allNumbers.forEach(number => {
      number.shiShen = dayMaster.getShiShenRelationship(number);
    });

    setCenterNumbers(center);
    setRightNumbers([n_c0_c1, dayMaster, n_c2_c3]);
    setLeftNumbers([n_c0_c2, n_c1_c3, n_c0_c3]);
    setTopNumber(n_top);
    setBottomNumber(n_total_sum);
    setRightSumNumber(n_right_sum);
    setLeftSumNumber(n_left_sum);
  };



  const renderNumberDisplay = (
      num: ChartNumber | null,
      direction: 'row' | 'column' | 'row-reverse' | 'column-reverse' = 'row'
    ) => {
    if (!num) return null;
    const display = num.display();
    return (
      <View style={{ flexDirection: direction, alignItems: 'center' }}>
        <ThemedText style={[styles.infoText, { color: display.color }]}>
          {num.tianGan}{num.wuXing}
        </ThemedText>
        <ThemedText style={[styles.numberText, { color: display.color }]}>
          {display.value} 
        </ThemedText>
        <ThemedText style={[styles.infoText, { color: display.color }]}>
          {num.shiShen}
        </ThemedText>
      </View>
    );
  };

  return (
    <>
      <ThemedView style={styles.container}>
        <ThemedView style={styles.titleContainer}>
          <ThemedText style={styles.title}>数字排盘</ThemedText>
        </ThemedView>
        
        <View style={styles.contentContainer}>
          <Image 
            source={require('../assets/images/backgroundpic.jpg')} 
            style={styles.backgroundImage} 
            resizeMode="cover"
          />
        
          <View style={styles.inputContainer}>
            <TouchableOpacity
              style={[
                styles.dateButton,
                { backgroundColor: colorScheme === 'dark' ? '#757575' : '#CCCCCC' }
              ]}
              onPress={() => setShowDateModal(true)}
              activeOpacity={0.7}
            >
              <ThemedText style={styles.dateButtonText}>日期</ThemedText>
            </TouchableOpacity>
            <TextInput
              style={[
                styles.input,
                { 
                  borderColor: colorScheme === 'dark' ? '#ffffff' : '#000000',
                  color: colorScheme === 'dark' ? '#ffffff' : '#000000' 
                }
              ]}
              value={inputValue}
              onChangeText={setInputValue}
              placeholder="请输入至少4位数字"
              placeholderTextColor={colorScheme === 'dark' ? '#aaaaaa' : '#888888'}
              keyboardType="numeric"
            />
            <TouchableOpacity
              style={[
                styles.button,
                { backgroundColor: colorScheme === 'dark' ? '#5C6BC0' : '#64B5F6' }
              ]}
              onPress={() => { 
                calculateChart(); 
                setShowChart(true); 
                setShowBaseInfo(false); 
              }}
              activeOpacity={0.7}
            >
              <ThemedText 
                style={[
                  styles.buttonText, 
                  { color: '#FFFFFF' }
                ]}
              >
                确定
              </ThemedText>
            </TouchableOpacity>
          </View>
          {showBaseInfo && (
            <View style={styles.baseInfoContainer}>
              {/* 显示姓名 */}
              <View style={styles.dateInfoContainer}>
                {name ? (
                  <ThemedText style={styles.dateInfo}>
                    姓名：{name}
                  </ThemedText>
                ) : null}
              </View>          
              {/* 显示公历 */}
              <View style={styles.dateInfoContainer}>
                {name ? (
                  <ThemedText style={styles.dateInfo}>
                    {gongLiString}
                  </ThemedText>
                ) : null}
              </View>    
              {/* 显示农历日期信息 - 使用固定高度容器确保主chart位置不变 */}
              <View style={styles.dateInfoContainer}>
                {dateShowString ? (
                  <ThemedText style={styles.dateInfo}>
                    {dateShowString}
                  </ThemedText>
                ) : null}
              </View>
              {/* 显示四柱 */}
              <View style={styles.dateInfoContainer}>
                {dateShowString ? (
                  <ThemedText style={styles.dateInfo}>
                    四柱：{siZhuShowString}
                  </ThemedText>
                ) : null}
              </View>
            </View>
          )}

          {showChart && (
            <View style={styles.chartContainer}>
              {/* Top number */}
              <View style={styles.topNumberContainer}>
                {renderNumberDisplay(topNumber, 'row')}
              </View>

              {/* Center area with left and right numbers */}
              <View style={styles.centerAreaContainer}>
                {/* Connection lines for center to sides, and top/bottom lines */}
                <View style={styles.connectionLinesContainer}>
                {/* Bottom Connection */}
                  <View 
                    style={{
                      position: 'absolute',
                      bottom: '0%',
                      left: '10%',
                      right: '10%',
                      height: 6,
                      backgroundColor: '#df1414'
                    }}
                  />              
                  <View 
                    style={{
                      position: 'absolute',
                      top: '0%',
                      left: '10%',
                      right: '10%',
                      height: 6,
                      backgroundColor: '#df1414'
                    }}
                  />              

                  <View 
                    style={{
                      position: 'absolute',
                      top: '0%',
                      bottom: '0%',
                      left: '20%',
                      width: 2,
                      backgroundColor: '#df1414'
                    }}
                  />     
                  <View 
                    style={{
                      position: 'absolute',
                      top: '0%',
                      bottom: '0%',
                      left: '40%',
                      width: 2,
                      backgroundColor: '#df1414'
                    }}
                  />    
                  <View 
                    style={{
                      position: 'absolute',
                      top: '0%',
                      bottom: '0%',
                      left: '60%',
                      width: 2,
                      backgroundColor: '#df1414'
                    }}
                  />    
                  <View 
                    style={{
                      position: 'absolute',
                      top: '0%',
                      bottom: '0%',
                      left: '80%',
                      width: 2,
                      backgroundColor: '#df1414'
                    }}
                  />                  
                  <View 
                    style={{
                      position: 'absolute',
                      top: '33.3%',
                      left: '20%',
                      right: '60%',
                      height: 6,
                      backgroundColor: '#df1414'
                    }}
                  />
                  <View 
                    style={{
                      position: 'absolute',
                      top: '66.67%',
                      left: '20%',
                      right: '60%',
                      height: 6,
                      backgroundColor: '#df1414'
                    }}
                  />  
                  <View 
                    style={{
                      position: 'absolute',
                      top: '33.3%',
                      left: '60%',
                      right: '20%',
                      height: 6,
                      backgroundColor: '#df1414'
                    }}
                  />  
                  <View 
                    style={{
                      position: 'absolute',
                      top: '66.67%',
                      left: '60%',
                      right: '20%',
                      height: 6,
                      backgroundColor: '#df1414'
                    }}
                  />    
                </View>
                
                
                {/* Left sum number */}
                <View style={styles.leftNumbersContainer}>
                  <View style={styles.sideNumberContainer}>
                    {renderNumberDisplay(leftSumNumber, 'column')}
                  </View>
                </View>

                
                {/* Left numbers */}
                <View style={styles.leftNumbersContainer}>
                  {leftNumbers.map((num, index) => (
                    <View key={`left-${index}`} style={styles.sideNumberContainer}>
                      {renderNumberDisplay(num, 'column')}
                    </View>
                  ))}
                </View>

                {/* Center numbers */}
                <View style={styles.centerNumbersContainer}>
                  {centerNumbers.map((num, index) => (
                    <View key={`center-${index}`} style={{ paddingVertical: 3 }}>
                      <ThemedText style={styles.centerNumberText}>
                        {num.value}
                      </ThemedText>
                    </View>
                  ))}
                </View>

                {/* Right numbers */}
                <View style={styles.rightNumbersContainer}>
                  {rightNumbers.map((num, index) => (
                    <View key={`right-${index}`} style={styles.sideNumberContainer}>
                      {renderNumberDisplay(num, 'column')}
                    </View>
                  ))}
                </View>

                {/* Left sum number */}
                <View style={styles.rightNumbersContainer}>
                  <View style={styles.sideNumberContainer}>
                    {renderNumberDisplay(rightSumNumber, 'column')}
                  </View>
                </View>
              </View>



              {/* Bottom number */}
              <View style={styles.bottomNumberContainer}>
                <View style={[styles.horizontalBottomLine, { backgroundColor: 'black' }]} />
                {renderNumberDisplay(bottomNumber, 'row')}
              </View>
            </View>
          )}
        </View>
      </ThemedView>

      {/* Date Modal */}
      <Modal
        visible={showDateModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowDateModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colorScheme === 'dark' ? '#333333' : '#FFFFFF' }]}>
            <ThemedText style={styles.modalTitle}>选择日期</ThemedText>
            <View style={[styles.modalRow, { justifyContent: 'flex-start' }]}>
              <TextInput
                style={[styles.modalInput, styles.nameInput]}
                placeholder="姓名"
                value={name}
                onChangeText={setName}
              />
            </View>
            <View style={[styles.modalRow, { justifyContent: 'flex-start' }]}>
              <TextInput
                style={styles.modalInput}
                placeholder="年"
                keyboardType="numeric"
                value={year}
                onChangeText={setYear}
              />
              <TextInput
                style={[styles.modalInput, styles.shortModalInput]}
                placeholder="月"
                keyboardType="numeric"
                value={month}
                onChangeText={setMonth}
              />
              <TextInput
                style={[styles.modalInput, styles.shortModalInput]}
                placeholder="日"
                keyboardType="numeric"
                value={day}
                onChangeText={setDay}
              />
            </View>
            <View style={[styles.modalRow, { justifyContent: 'flex-start' }]}>
              <TextInput
                style={[styles.modalInput, styles.shortModalInput]}
                placeholder="时"
                keyboardType="numeric"
                value={hour}
                onChangeText={setHour}
              />
              <ThemedText style={styles.timeColon}>:</ThemedText>
              <TextInput
                style={[styles.modalInput, styles.shortModalInput]}
                placeholder="分"
                keyboardType="numeric"
                value={minute}
                onChangeText={setMinute}
              />
            </View>
            <View style={[styles.modalRow, { justifyContent: 'flex-start' }]}>
              <TouchableOpacity
                style={[styles.calendarOption, calendarType === 'Gregorian' && styles.selectedOption]}
                onPress={() => setCalendarType('Gregorian')}
              >
                <ThemedText>公历</ThemedText>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.calendarOption, calendarType === 'Lunar' && styles.selectedOption]}
                onPress={() => setCalendarType('Lunar')}
              >
                <ThemedText>农历</ThemedText>
              </TouchableOpacity>
            </View>
            {calendarType === 'Lunar' && (
              <View style={[styles.modalRow, { justifyContent: 'flex-start' }]}>
                <ThemedText style={{ marginRight: 10 }}>闰月</ThemedText>
                <Switch value={isLeapMonth} onValueChange={setIsLeapMonth} />
              </View>
            )}
            <View style={[styles.modalRow, { justifyContent: 'flex-end' }]}>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: '#AAAAAA', marginRight: 10 }]}
                onPress={() => setShowDateModal(false)}
              >
                <ThemedText style={{ color: '#FFFFFF' }}>取消</ThemedText>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: '#5C6BC0' }]}
                onPress={handleDateConfirm}
              >
                <ThemedText style={{ color: '#FFFFFF' }}>确定</ThemedText>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
};

const windowWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
  contentContainer: {
    flex: 1,
    width: '100%',
    alignItems: 'center',
    position: 'relative',
  },
  backgroundImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    opacity: 0.4, // 这里设置透明度，0完全透明，1完全不透明
  },
  container: {
    flex: 1,
    alignItems: 'center',
  },
  titleContainer: {
    width: '100%',
    padding: 15,
    alignItems: 'center',
    backgroundColor: '#A1CEDC',
    minHeight: 60,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    lineHeight: 30,
  },
  inputContainer: {
    flexDirection: 'row',
    width: '100%',
    padding: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  baseInfoContainer: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    margin: 0,
  },
  dateInfoContainer: {
    height: 25, // 固定高度，确保主图表位置不变
    justifyContent: 'center',
    width: '90%',
  },
  dateInfo: {
    fontSize: 18,
    textAlign: 'left',
    color: '#555',
  },
  timeColon: {
    fontSize: 18,
    marginHorizontal: 5,
  },
  input: {
    width: '55%',
    height: 40,
    borderWidth: 1,
    paddingHorizontal: 10,
    fontSize: 17,
    textAlignVertical: 'center',
    paddingTop: 0,
    paddingBottom: 0,
  },
  button: {
    marginLeft: 10,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    borderWidth: 0,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
  },
  buttonText: {
    fontWeight: 'bold',
    fontSize: 16,
    textAlign: 'center',
  },
  dateButton: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
    marginRight: 10,
  },
  dateButtonText: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  chartContainer: {
    width: windowWidth * 0.9,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 5,
  },
  topNumberContainer: {
    alignItems: 'center',
    marginBottom: 10,
  },

  centerAreaContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 0,
    position: 'relative',
  },
  leftNumbersContainer: {
    justifyContent: 'space-around',
    height: 350,
    zIndex: 2,
    marginTop: 0
  },
  rightNumbersContainer: {
    justifyContent: 'space-around',
    height: 350,
    zIndex: 2,
    marginTop: 0,
  },
  centerNumbersContainer: {
    justifyContent: 'space-around',
    height: 350,
    zIndex: 2,
  },
  sideNumberContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  numberText: {
    fontSize: 32,
    lineHeight: 40,
    marginHorizontal: 0,
    paddingVertical: 0,
    includeFontPadding: true,
    fontFamily: 'serif',
    fontWeight: 'bold',
  },
  infoText: {
    fontSize: 18,
    marginHorizontal: 5,
  },
  centerNumberText: {
    fontSize: 42,
    lineHeight: 46,
    paddingVertical: 2,
    includeFontPadding: true,
    fontFamily: 'serif',
    fontWeight: 'bold',
  },
  connectionLinesContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    zIndex: 1,
  },
  verticalConnectionLine: {
    position: 'absolute',
    width: 1,
  },
  horizontalConnectionLine: {
    position: 'absolute',
    height: 1,
  },
  verticalBracketLine: {
    position: 'absolute',
    width: 1,
  },
  blackContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: -40,
    bottom: -40,
    zIndex: 0,
  },
  blackTopBracket: {
    position: 'absolute',
    borderColor: 'black',
    borderTopWidth: 1,
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderTopLeftRadius: 5,
    borderTopRightRadius: 5,
  },
  blackBottomLine: {
    position: 'absolute',
    height: 1,
    backgroundColor: 'black',
  },
  blackVerticalLine: {
    position: 'absolute',
    width: 1,
    backgroundColor: 'black',
  },
  bottomNumberContainer: {
    alignItems: 'center',
    marginTop: 10,
  },
  horizontalBottomLine: {
    width: '100%',
    height: 1,
    marginBottom: 10,
  },
  selectedOption: {
    backgroundColor: '#64B5F6',
    borderColor: '#64B5F6',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '85%',
    borderRadius: 10,
    padding: 20,
    paddingLeft: 30,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  modalRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 5,
  },
  modalInput: {
    borderWidth: 1,
    borderColor: '#999',
    height: 35,
    borderRadius: 5,
    paddingVertical: 0,
    paddingHorizontal: 10,
    width: '30%',
    marginHorizontal: 3,
    textAlign: 'left',
  },
  shortModalInput: {
    width: '18%'
  },
  nameInput: {
    width: '70%',
  },
  modalButton: {
    paddingVertical: 8,
    paddingHorizontal: 20,
    borderRadius: 6,
  },
  calendarOption: {
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#888',
    marginHorizontal: 5,
  },
});
