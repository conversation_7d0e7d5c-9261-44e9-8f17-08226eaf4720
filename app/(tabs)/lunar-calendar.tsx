import React, { useState } from 'react';
import { View, Text, StyleSheet, Button, ScrollView, SafeAreaView, StatusBar, Platform, Modal, TextInput, TouchableOpacity } from 'react-native';
import { LunarCalendar, SunMoonInfo } from '@/components/LunarComponent';
import { Stack } from 'expo-router';

export default function LunarDemoScreen() {
  const [currentDate] = useState(new Date());
  const [currentYear, setCurrentYear] = useState(currentDate.getFullYear());
  const [currentMonth, setCurrentMonth] = useState(currentDate.getMonth() + 1);
  const [showingCalendar, setShowingCalendar] = useState(true);
  
  // 新增状态用于控制模态框的显示和输入值
  const [modalVisible, setModalVisible] = useState(false);
  const [inputYear, setInputYear] = useState(currentYear.toString());
  const [inputMonth, setInputMonth] = useState(currentMonth.toString());
  
  // Default location - Beijing
  const defaultLocation = {
    longitude: 116.3833,
    latitude: 39.9167,
    elevation: 44, // meters above sea level
  };
  
  const nextMonth = () => {
    if (currentMonth === 12) {
      setCurrentMonth(1);
      setCurrentYear(currentYear + 1);
    } else {
      setCurrentMonth(currentMonth + 1);
    }
  };
  
  const prevMonth = () => {
    if (currentMonth === 1) {
      setCurrentMonth(12);
      setCurrentYear(currentYear - 1);
    } else {
      setCurrentMonth(currentMonth - 1);
    }
  };
  
  const nextYear = () => {
    setCurrentYear(currentYear + 1);
  };
  
  const prevYear = () => {
    setCurrentYear(currentYear - 1);
  };

  // 新增函数用于打开模态框
  const openDateInputModal = () => {
    setInputYear(currentYear.toString());
    setInputMonth(currentMonth.toString());
    setModalVisible(true);
  };

  // 新增函数用于提交年月输入
  const submitDateInput = () => {
    const newYear = parseInt(inputYear);
    const newMonth = parseInt(inputMonth);
    
    // 验证输入的有效性：FullLunar.js支持-722年到未来的日期
    if (isNaN(newYear) || isNaN(newMonth) || newYear < -4712 || newYear > 9999 || newMonth < 1 || newMonth > 12) {
      alert('请输入有效的年月！年份范围：-4712年至9999年，月份范围：1-12');
      return;
    }
    
    setCurrentYear(newYear);
    setCurrentMonth(newMonth);
    setModalVisible(false);
  };

  return (
    <>
      <StatusBar
        backgroundColor="white" 
        barStyle="dark-content"
        translucent={false}
      />
      
      <SafeAreaView style={[styles.container, { paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0 }]}>
        <Stack.Screen 
          options={{
            headerShown: false,
          }}
        />
        

        
        {showingCalendar ? (
          <ScrollView style={styles.content}>
            <View style={styles.navRow}>
              <TouchableOpacity 
                style={styles.navButton} 
                onPress={prevYear}
              >
                <Text style={styles.navButtonText}>{'<<'}</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.navButton} 
                onPress={prevMonth}
              >
                <Text style={styles.navButtonText}>{'<'}</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.dateButton} 
                onPress={openDateInputModal}
              >
                <Text style={styles.dateText}>{currentYear}</Text>
                <Text style={styles.dateText}>年</Text>
                <Text style={styles.dateText}>{currentMonth}</Text>
                <Text style={styles.dateText}>月</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.navButton} 
                onPress={nextMonth}
              >
                <Text style={styles.navButtonText}>{'>'}</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.navButton} 
                onPress={nextYear}
              >
                <Text style={styles.navButtonText}>{'>>'}</Text>
              </TouchableOpacity>
            </View>
            
            <LunarCalendar 
              year={currentYear}
              month={currentMonth}
            />
            

          </ScrollView>
        ) : (
          <ScrollView style={styles.content}>
            <Text style={styles.locationText}>
              Location: Beijing (Longitude: {defaultLocation.longitude}°, Latitude: {defaultLocation.latitude}°)
            </Text>
            
            <SunMoonInfo
              date={currentDate}
              longitude={defaultLocation.longitude}
              latitude={defaultLocation.latitude}
              elevation={defaultLocation.elevation}
            />
            
          </ScrollView>
        )}

        {/* 年月输入模态框 */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={modalVisible}
          onRequestClose={() => setModalVisible(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>请输入年月</Text>
              
              <View style={styles.inputRow}>
                <Text style={styles.inputLabel}>年份：</Text>
                <TextInput
                  style={styles.input}
                  value={inputYear}
                  onChangeText={setInputYear}
                  keyboardType="number-pad"
                  placeholder="-4712至9999"
                  maxLength={5}
                />
              </View>
              
              <View style={styles.inputRow}>
                <Text style={styles.inputLabel}>月份：</Text>
                <TextInput
                  style={styles.input}
                  value={inputMonth}
                  onChangeText={setInputMonth}
                  keyboardType="number-pad"
                  placeholder="1-12"
                  maxLength={2}
                />
              </View>
              
              <View style={styles.modalButtons}>
                <TouchableOpacity 
                  style={[styles.modalButton, styles.cancelButton]} 
                  onPress={() => setModalVisible(false)}
                >
                  <Text style={styles.buttonText}>取消</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[styles.modalButton, styles.confirmButton]} 
                  onPress={submitDateInput}
                >
                  <Text style={styles.buttonText}>确定</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  navButton: {
    backgroundColor: '#64B5F6',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    minWidth: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  navButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
  dateButton: {
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 4,
    flexDirection: 'row', // 水平排列子元素
    alignItems: 'center', // 垂直居中
    minWidth: 100, // 确保有足够的水平空间
    justifyContent: 'center', // 水平居中
    backgroundColor: '#f8f8f8', // 添加背景色使按钮更明显
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  content: {
    flex: 1,
  },
  navRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 15,
  },
  dateText: {
    fontSize: 16, // 稍微减小字体大小
    fontWeight: 'bold',
    marginHorizontal: 1, // 添加水平间距
  },
  locationText: {
    padding: 15,
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  explanationBox: {
    margin: 15,
    padding: 15,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  explanationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  explanationText: {
    fontSize: 14,
    lineHeight: 20,
  },
  // 模态框样式
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginBottom: 15,
  },
  inputLabel: {
    width: 60,
    fontSize: 16,
  },
  input: {
    flex: 1,
    height: 40,   
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    paddingHorizontal: 10,
    paddingVertical: 0,
    textAlignVertical: 'center',
    backgroundColor: '#fff',
    fontSize: 16,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 10,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 10,
    borderRadius: 5,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: '#ccc',
  },
  confirmButton: {
    backgroundColor: '#4CAF50',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
});