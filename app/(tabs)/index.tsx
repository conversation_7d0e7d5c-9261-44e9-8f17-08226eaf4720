import { StyleSheet, Platform, SafeAreaView, StatusBar } from 'react-native';
import React from 'react';
import { ThemedView } from '@/components/ThemedView';
import { NumberChart } from '@/components/NumberChart';
import { useColorScheme } from '@/hooks/useColorScheme';



export default function HomeScreen() {
  const colorScheme = useColorScheme();

  return (
    <SafeAreaView style={[styles.container, { paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0 }]}>
      <StatusBar
        barStyle={colorScheme === 'dark' ? 'light-content' : 'dark-content'}
        backgroundColor="transparent"
        translucent
      />
      <ThemedView style={{ flex: 1 }}>
        <NumberChart />
      </ThemedView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  }
});
